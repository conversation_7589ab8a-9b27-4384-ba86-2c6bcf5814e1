import 'package:flutter/material.dart';
import '../constants/app_colors.dart';
import '../constants/app_constants.dart';
import '../widgets/dashboard_card.dart';
import '../widgets/app_logo.dart';
import '../services/screen_reader_service.dart';
import '../responsive/responsive.dart';
import 'accounts_screen.dart';
import 'journal_entries_screen.dart';
import 'customers_suppliers_screen.dart';
import 'warehouse_screen.dart';
import 'reports_screen.dart';
import 'invoices_screen.dart';
import 'settings_screen.dart';
import 'audit_log_screen.dart';
import 'advanced_dashboard_screen.dart';
import 'inventory_count_screen.dart';
import 'quotations_screen.dart';
import 'recurring_invoices_screen.dart';
import 'invoice_templates_screen.dart';
import 'interactive_report_screen.dart';
import 'backup_management_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutBack,
          ),
        );

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScreenInfoProvider(
      child: Scaffold(
        body: Container(
          decoration: const BoxDecoration(
            gradient: AppColors.backgroundGradient,
          ),
          child: SafeArea(
            child: FadeTransition(
              opacity: _fadeAnimation,
              child: SlideTransition(
                position: _slideAnimation,
                child: ResponsiveLayout(
                  mobile: _buildMobileLayout(),
                  tablet: _buildTabletLayout(),
                  desktop: _buildDesktopLayout(),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  // تخطيط الهواتف المحمولة
  Widget _buildMobileLayout() {
    return Column(
      children: [
        _buildHeader(),
        Expanded(child: _buildDashboard()),
      ],
    );
  }

  // تخطيط الأجهزة اللوحية
  Widget _buildTabletLayout() {
    return Column(
      children: [
        _buildHeader(),
        Expanded(child: _buildTabletDashboard()),
      ],
    );
  }

  // تخطيط أجهزة سطح المكتب
  Widget _buildDesktopLayout() {
    return Row(
      children: [
        // الشريط الجانبي
        Container(
          width: context.dimensions.sidebarWidth,
          decoration: BoxDecoration(
            color: AppColors.surface.withValues(alpha: 0.9),
            boxShadow: [
              BoxShadow(
                color: AppColors.shadow,
                blurRadius: 10,
                offset: const Offset(2, 0),
              ),
            ],
          ),
          child: _buildSidebar(),
        ),
        // المحتوى الرئيسي
        Expanded(
          child: Column(
            children: [
              _buildDesktopHeader(),
              Expanded(child: _buildDesktopDashboard()),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildHeader() {
    final dimensions = context.dimensions;
    return ResponsiveContainer(
      padding: EdgeInsets.all(dimensions.paddingL),
      child: Column(
        children: [
          Semantics(
            label: 'شعار تطبيق دفتر الحسابات الذكي',
            child: AppLogo(size: dimensions.iconSizeXL * 2),
          ),
          ResponsiveSpacing.medium(),
          Semantics(
            header: true,
            label: 'عنوان التطبيق: ${AppConstants.appNameArabic}',
            child: ResponsiveText.h1(
              AppConstants.appNameArabic,
              style: const TextStyle(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          ResponsiveSpacing.small(),
          Semantics(
            label: 'وصف التطبيق: نظام محاسبة متكامل للشركات السورية',
            child: ResponsiveText.body(
              'نظام محاسبة متكامل للشركات السورية',
              style: const TextStyle(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
          ),
          ResponsiveSpacing.large(),
          _buildQuickStats(),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    final dimensions = context.dimensions;
    return Semantics(
      label: 'إحصائيات سريعة للنظام',
      child: ResponsiveCard(
        padding: EdgeInsets.all(dimensions.paddingM),
        child: AdaptiveLayout(
          mainAxisAlignment: MainAxisAlignment.spaceAround,
          spacing: dimensions.spacingM,
          children: [
            _buildStatItem('💰', '0', 'إجمالي الأصول'),
            _buildStatItem('📈', '0', 'المبيعات اليوم'),
            _buildStatItem('📊', '0', 'عدد الحسابات'),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String icon, String value, String label) {
    return Semantics(
      label: '$label: $value',
      child: Column(
        children: [
          Semantics(
            label: 'أيقونة $label',
            child: Text(icon, style: const TextStyle(fontSize: 24)),
          ),
          const SizedBox(height: 4),
          Semantics(
            label: 'القيمة: $value',
            child: Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                color: AppColors.primary,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Semantics(
            label: 'التسمية: $label',
            child: Text(
              label,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: AppColors.textSecondary),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDashboard() {
    return ResponsiveContainer(
      child: Semantics(
        label: 'لوحة التحكم الرئيسية - قائمة الوظائف المتاحة',
        child: ResponsiveGrid(columns: 2, children: _getDashboardCards()),
      ),
    );
  }

  void _navigateToAccounts() {
    ScreenReaderService.announceScreenChange('دليل الحسابات');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AccountsScreen()),
    );
  }

  void _navigateToJournalEntries() {
    ScreenReaderService.announceScreenChange('القيود المحاسبية');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const JournalEntriesScreen()),
    );
  }

  void _navigateToInvoices() {
    ScreenReaderService.announceScreenChange('الفواتير');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const InvoicesScreen()),
    );
  }

  void _navigateToCustomersSuppliers() {
    ScreenReaderService.announceScreenChange('العملاء والموردين');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const CustomersSuppliersScreen()),
    );
  }

  void _navigateToWarehouse() {
    ScreenReaderService.announceScreenChange('المستودع والجرد');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const WarehouseScreen()),
    );
  }

  void _navigateToReports() {
    ScreenReaderService.announceScreenChange('التقارير');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const ReportsScreen()),
    );
  }

  void _navigateToSettings() {
    ScreenReaderService.announceScreenChange('الإعدادات');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SettingsScreen()),
    );
  }

  void _navigateToAuditLog() {
    ScreenReaderService.announceScreenChange('سجل المراجعة');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AuditLogScreen()),
    );
  }

  void _navigateToAdvancedDashboard() {
    ScreenReaderService.announceScreenChange('لوحة التحكم المتقدمة');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const AdvancedDashboardScreen()),
    );
  }

  void _navigateToInventoryCount() {
    ScreenReaderService.announceScreenChange('إدارة الجرد');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const InventoryCountScreen()),
    );
  }

  void _navigateToQuotations() {
    ScreenReaderService.announceScreenChange('عروض الأسعار');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const QuotationsScreen()),
    );
  }

  void _navigateToRecurringInvoices() {
    ScreenReaderService.announceScreenChange('الفواتير المتكررة');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const RecurringInvoicesScreen()),
    );
  }

  void _navigateToInvoiceTemplates() {
    ScreenReaderService.announceScreenChange('قوالب الفواتير');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const InvoiceTemplatesScreen()),
    );
  }

  void _navigateToInteractiveReports() {
    ScreenReaderService.announceScreenChange('التقارير التفاعلية');
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => const InteractiveReportScreen(
          reportType: 'sales_analysis',
          reportTitle: 'تحليل المبيعات التفاعلي',
        ),
      ),
    );
  }

  void _navigateToBackupManagement() {
    ScreenReaderService.announceScreenChange('إدارة النسخ الاحتياطية');
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const BackupManagementScreen()),
    );
  }

  // تخطيط لوحة التحكم للأجهزة اللوحية
  Widget _buildTabletDashboard() {
    return ResponsiveContainer(
      child: ResponsiveGrid(columns: 3, children: _getDashboardCards()),
    );
  }

  // تخطيط لوحة التحكم لأجهزة سطح المكتب
  Widget _buildDesktopDashboard() {
    return ResponsiveContainer(
      child: ResponsiveGrid(columns: 4, children: _getDashboardCards()),
    );
  }

  // رأس الصفحة لأجهزة سطح المكتب
  Widget _buildDesktopHeader() {
    final dimensions = context.dimensions;
    return ResponsiveContainer(
      padding: EdgeInsets.all(dimensions.paddingL),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                ResponsiveText.h2(
                  'مرحباً بك في ${AppConstants.appNameArabic}',
                  style: const TextStyle(
                    color: AppColors.primary,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ResponsiveSpacing.small(),
                ResponsiveText.body(
                  'نظام محاسبة متكامل للشركات السورية',
                  style: const TextStyle(color: AppColors.textSecondary),
                ),
              ],
            ),
          ),
          _buildQuickStats(),
        ],
      ),
    );
  }

  // الشريط الجانبي لأجهزة سطح المكتب
  Widget _buildSidebar() {
    final dimensions = context.dimensions;
    return ResponsiveContainer(
      padding: EdgeInsets.all(dimensions.paddingL),
      child: Column(
        children: [
          // الشعار والعنوان
          AppLogo(size: dimensions.iconSizeXL),
          ResponsiveSpacing.medium(),
          ResponsiveText.h3(
            AppConstants.appNameArabic,
            style: const TextStyle(
              color: AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          ResponsiveSpacing.large(),

          // قائمة التنقل
          Expanded(
            child: ListView(
              children: [
                _buildSidebarItem(
                  icon: Icons.account_tree,
                  title: 'دليل الحسابات',
                  onTap: _navigateToAccounts,
                ),
                _buildSidebarItem(
                  icon: Icons.receipt_long,
                  title: 'القيود المحاسبية',
                  onTap: _navigateToJournalEntries,
                ),
                _buildSidebarItem(
                  icon: Icons.description,
                  title: 'الفواتير',
                  onTap: _navigateToInvoices,
                ),
                _buildSidebarItem(
                  icon: Icons.people,
                  title: 'العملاء والموردين',
                  onTap: _navigateToCustomersSuppliers,
                ),
                _buildSidebarItem(
                  icon: Icons.inventory,
                  title: 'المستودع والجرد',
                  onTap: _navigateToWarehouse,
                ),
                _buildSidebarItem(
                  icon: Icons.analytics,
                  title: 'التقارير',
                  onTap: _navigateToReports,
                ),
                _buildSidebarItem(
                  icon: Icons.settings,
                  title: 'الإعدادات',
                  onTap: _navigateToSettings,
                ),
                _buildSidebarItem(
                  icon: Icons.history,
                  title: 'سجل المراجعة',
                  onTap: _navigateToAuditLog,
                ),
                // الميزات الجديدة
                _buildSidebarItem(
                  icon: Icons.inventory_2,
                  title: 'إدارة الجرد',
                  onTap: _navigateToInventoryCount,
                ),
                _buildSidebarItem(
                  icon: Icons.request_quote,
                  title: 'عروض الأسعار',
                  onTap: _navigateToQuotations,
                ),
                _buildSidebarItem(
                  icon: Icons.repeat,
                  title: 'الفواتير المتكررة',
                  onTap: _navigateToRecurringInvoices,
                ),
                _buildSidebarItem(
                  icon: Icons.design_services,
                  title: 'قوالب الفواتير',
                  onTap: _navigateToInvoiceTemplates,
                ),
                _buildSidebarItem(
                  icon: Icons.insights,
                  title: 'التقارير التفاعلية',
                  onTap: _navigateToInteractiveReports,
                ),
                _buildSidebarItem(
                  icon: Icons.backup,
                  title: 'النسخ الاحتياطية',
                  onTap: _navigateToBackupManagement,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // عنصر في الشريط الجانبي
  Widget _buildSidebarItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    final dimensions = context.dimensions;
    return ResponsiveCard(
      margin: EdgeInsets.only(bottom: dimensions.spacingS),
      onTap: onTap,
      child: Row(
        children: [
          ResponsiveIcon.medium(icon, color: AppColors.primary),
          ResponsiveSpacing.medium(isVertical: false),
          Expanded(
            child: ResponsiveText.body(
              title,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
        ],
      ),
    );
  }

  // الحصول على قائمة بطاقات لوحة التحكم
  List<Widget> _getDashboardCards() {
    return [
      DashboardCard(
        title: 'لوحة التحكم المتقدمة',
        subtitle: 'مؤشرات الأداء والرسوم البيانية',
        icon: Icons.dashboard,
        color: AppColors.primary,
        onTap: () => _navigateToAdvancedDashboard(),
      ),
      DashboardCard(
        title: 'دليل الحسابات',
        subtitle: 'إدارة الحسابات المحاسبية',
        icon: Icons.account_tree,
        color: AppColors.secondary,
        onTap: () => _navigateToAccounts(),
      ),
      DashboardCard(
        title: 'القيود المحاسبية',
        subtitle: 'إدخال وإدارة القيود',
        icon: Icons.receipt_long,
        color: AppColors.success,
        onTap: () => _navigateToJournalEntries(),
      ),
      DashboardCard(
        title: 'الفواتير',
        subtitle: 'فواتير المبيعات والمشتريات',
        icon: Icons.description,
        color: AppColors.info,
        onTap: () => _navigateToInvoices(),
      ),
      DashboardCard(
        title: 'العملاء والموردين',
        subtitle: 'إدارة العملاء والموردين',
        icon: Icons.people,
        color: AppColors.warning,
        onTap: () => _navigateToCustomersSuppliers(),
      ),
      DashboardCard(
        title: 'المستودع والجرد',
        subtitle: 'إدارة المخزون والجرد',
        icon: Icons.inventory,
        color: AppColors.info,
        onTap: () => _navigateToWarehouse(),
      ),
      DashboardCard(
        title: 'التقارير',
        subtitle: 'التقارير المالية والإحصائيات',
        icon: Icons.analytics,
        color: AppColors.error,
        onTap: () => _navigateToReports(),
      ),
      DashboardCard(
        title: 'الإعدادات',
        subtitle: 'إعدادات النظام والشركة',
        icon: Icons.settings,
        color: AppColors.textSecondary,
        onTap: () => _navigateToSettings(),
      ),
      DashboardCard(
        title: 'سجل المراجعة',
        subtitle: 'مراجعة العمليات والأنشطة',
        icon: Icons.history,
        color: AppColors.primary,
        onTap: () => _navigateToAuditLog(),
      ),
      // الميزات الجديدة
      DashboardCard(
        title: 'إدارة الجرد',
        subtitle: 'جرد المخزون والعد الدوري',
        icon: Icons.inventory_2,
        color: Colors.teal,
        onTap: () => _navigateToInventoryCount(),
      ),
      DashboardCard(
        title: 'عروض الأسعار',
        subtitle: 'إنشاء وإدارة عروض الأسعار',
        icon: Icons.request_quote,
        color: Colors.indigo,
        onTap: () => _navigateToQuotations(),
      ),
      DashboardCard(
        title: 'الفواتير المتكررة',
        subtitle: 'فواتير دورية ومجدولة',
        icon: Icons.repeat,
        color: Colors.purple,
        onTap: () => _navigateToRecurringInvoices(),
      ),
      DashboardCard(
        title: 'قوالب الفواتير',
        subtitle: 'تصميم وإدارة قوالب الطباعة',
        icon: Icons.design_services,
        color: Colors.orange,
        onTap: () => _navigateToInvoiceTemplates(),
      ),
      DashboardCard(
        title: 'التقارير التفاعلية',
        subtitle: 'تقارير ديناميكية ورسوم بيانية',
        icon: Icons.insights,
        color: Colors.cyan,
        onTap: () => _navigateToInteractiveReports(),
      ),
      DashboardCard(
        title: 'النسخ الاحتياطية',
        subtitle: 'إدارة وحماية البيانات',
        icon: Icons.backup,
        color: Colors.green,
        onTap: () => _navigateToBackupManagement(),
      ),
    ];
  }
}
